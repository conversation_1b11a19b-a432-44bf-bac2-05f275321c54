import sql from 'mssql';
import {getDbSecretsFromKeyVault} from './keyvault';

let poolPromise: Promise<sql.ConnectionPool>;

const isLocal = process.env.NODE_ENV !== "production";

export async function initDbConnection() {

    const secrets = await getDbSecretsFromKeyVault();
    let dbConfig: sql.config = {
        server: secrets.DbServer,
        database: secrets.DbName,
        options: {
            encrypt: true,
        },
        authentication: {
            type: "azure-active-directory-default",
            options: {},
        },
    };
    if (isLocal) {
        dbConfig = {
            user: secrets.DbUser,
            password: secrets.DbPassword,
            server: secrets.DbServer,
            database: secrets.DbName,
            options: {
                encrypt: true,
                trustServerCertificate: false,
            },
        };
    }

    poolPromise = new sql.ConnectionPool(dbConfig)
        .connect()
        .then(pool => {
            console.log('✅ Connected to SQL Server');
            return pool;
        })
        .catch(err => {
            console.error('❌ Database connection failed:', err);
            process.exit(1);
        });

    return poolPromise;
}

export {sql, poolPromise};
