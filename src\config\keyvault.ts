import { ClientSecretCredential, DefaultAzureCredential } from '@azure/identity';
import { SecretClient } from '@azure/keyvault-secrets';

import 'dotenv/config';

export interface Secrets {
  AzureContainerName: string;
  AzureStorageAccountKey: string;
  AzureStorageAccountName: string;
  ClientName: string;
  DbName: string;
  DbPassword: string;
  DbServer: string;
  DbUser: string;
  StripeSecretKey: string;
  StripeWebhookSecret: string;
  FrontendUrl: string;
  SpClientId: string;
  SpClientSecret: string;
  SpTenantId: string;
  SpTokenScope: string;
}

// In-memory cache for secrets
let cachedSecrets: Secrets | null = null;
let cacheTimestamp: number | null = null;
const CACHE_LIFETIME_MS = 3600 * 1000; 

export async function getDbSecretsFromKeyVault(): Promise<Secrets> {
  // Check if secrets are already cached and not expired
  if (cachedSecrets && cacheTimestamp && (Date.now() - cacheTimestamp < CACHE_LIFETIME_MS)) {
    console.log('✅ Secrets fetched from CACHE.'); // Log for cached data
    return cachedSecrets;
  }

  // If not cached or expired, proceed to fetch from Key Vault
  // console.log('🔑 Fetching secrets from Azure Key Vault...'); // Log for Key Vault fetch

  const tenantId = process.env.SP_TENANT_ID;
  const clientId = process.env.SP_CLIENT_KEYVAULT;
  const clientSecret = process.env.SP_CLIENT_SECRET_KEYVAULT;
  const keyVaultName = process.env.AZURE_KEYVAULT_NAME;

  if (!tenantId || !clientId || !clientSecret || !keyVaultName) {
    throw new Error("Missing required environment variables for Key Vault access");
  }

  const keyVaultUrl = `https://${keyVaultName}.vault.azure.net`;
  const credential = new ClientSecretCredential(tenantId, clientId, clientSecret);
  const client = new SecretClient(keyVaultUrl, credential);

  const secretNames: (keyof Secrets)[] = [
    'AzureContainerName',
    'AzureStorageAccountKey',
    'AzureStorageAccountName',
    'ClientName',
    'DbName',
    'DbPassword',
    'DbServer',
    'DbUser',
    'StripeSecretKey',
    'StripeWebhookSecret',
    'FrontendUrl',
    'SpClientId',
    'SpClientSecret',
    'SpTenantId',
    'SpTokenScope',
  ];

  const secrets: Partial<Secrets> = {};

  for (const name of secretNames) {
    try {
      const result = await client.getSecret(name);
      if (!result.value) {
        throw new Error(`Secret ${name} is empty or undefined`);
      }
      secrets[name] = result.value;
    } catch (error) {
      throw new Error(`Failed to retrieve secret ${name}: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  for (const name of secretNames) {
    if (!secrets[name]) {
      throw new Error(`Missing required secret: ${name}`);
    }
  }

  cachedSecrets = secrets as Secrets;
  cacheTimestamp = Date.now();
  // console.log('💾 Secrets successfully fetched from Key Vault and cached.'); 

  return cachedSecrets;
}