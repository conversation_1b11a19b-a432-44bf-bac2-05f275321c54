{"name": "stripe-backend", "version": "1.0.0", "main": "server.ts", "scripts": {"dev": "ts-node-dev src/server.ts", "start": "node dist/server.js", "test": "echo \"Error: no test specified\" && exit 1"}, "keywords": [], "author": "", "license": "ISC", "description": "", "dependencies": {"@azure/identity": "^4.10.1", "@azure/keyvault-secrets": "^4.10.0", "axios": "^1.9.0", "body-parser": "^2.2.0", "cors": "^2.8.5", "dotenv": "^16.4.7", "express": "^4.21.2", "mssql": "^11.0.1", "mysql2": "^3.14.0", "sequelize": "^6.37.7", "stripe": "^17.7.0", "swagger-jsdoc": "^6.2.8", "swagger-ui-express": "^5.0.1"}, "devDependencies": {"@types/cors": "^2.8.17", "@types/express": "^5.0.1", "@types/mssql": "^9.1.7", "@types/stripe": "^8.0.416", "@types/swagger-jsdoc": "^6.0.4", "@types/swagger-ui-express": "^4.1.8", "nodemon": "^3.1.9", "ts-node-dev": "^2.0.0"}}