# Node.js
# Build a general Node.js project with npm.
# Add steps that analyze code, save build artifacts, deploy, and more:
# https://docs.microsoft.com/azure/devops/pipelines/languages/javascript

trigger:
- main
- master

pool:
  vmImage: ubuntu-latest

steps:
- task: NodeTool@0
  inputs:
    versionSpec: '22.x'
  displayName: 'Install Node.js'

- script: |
    npm install
  displayName: 'npm install'

- script: npx tsc
  displayName: 'Build TypeScript'

- script: |
    echo "Listing root"
    ls -al
    echo "Listing dist"
    ls -al ./dist
  displayName: 'Check dist folder contents'

- task: CopyFiles@2
  inputs:
      contents: |
        dist/**
        package.json
        package-lock.json
        .env
      targetFolder: $(Build.ArtifactStagingDirectory)
  displayName: 'Copy files to staging'

- task: ArchiveFiles@2
  inputs:
      rootFolderOrFile: '$(Build.ArtifactStagingDirectory)'
      includeRootFolder: false
      archiveType: 'zip'
      archiveFile: '$(Build.ArtifactStagingDirectory)/app.zip'
      replaceExistingArchive: true
  displayName: 'Archive app.zip'

- task: PublishBuildArtifacts@1
  inputs:
      PathtoPublish: '$(Build.ArtifactStagingDirectory)/app.zip'
      ArtifactName: 'drop'
  displayName: 'Publish build artifact'