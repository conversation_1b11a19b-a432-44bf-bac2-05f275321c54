import express, { Request, Response } from 'express';
import cors from 'cors';
import dotenv from 'dotenv';
import { initDbConnection, poolPromise } from './config/db'; 
import swaggerJsdoc from 'swagger-jsdoc';
import swaggerUi from 'swagger-ui-express';
import { createSubscription, handleWebhook, getUserByStripeCustomerId } from './controllers/subscriptionController';

dotenv.config();

const app: express.Application = express(); 

app.use(cors());

app.use('/api/v1/hooks',express.raw({ type: 'application/json' }), handleWebhook); 

app.use(express.json());

app.get('/api/test', async (req: Request, res: Response) => {
    try {
        const pool = await poolPromise; 
        const result = await pool.request().query('SELECT GETDATE() AS current_time'); 
        res.json({ message: "API is working!", currentTime: result.recordset[0].current_time });
    } catch (error) {
        console.error(error);
        res.status(500).json({ message: 'Database query failed', error });
    }
});

// Swagger Docs Setup
const swaggerOptions = {
    definition: {
        openapi: "3.0.0",
        info: {
            title: "Subscription API",
            version: "1.0.0",
            description: "API for managing subscriptions with Stripe and SQL Server",
        },
        servers: [{ url: "http://localhost:5000", description: "Local Server" }, { url: "https://app02-sn-nonprod-dev-shared-payment-api-twa-abb3evfbhnhqhrbx.canadacentral-01.azurewebsites.net/", description: "Development Server" }],
    },
    apis:  [__filename, "./src/routes/*.ts", "./src/controllers/*.ts", "./dist/routes/*.js", "./dist/controllers/*.js"],
}

const swaggerDocs = swaggerJsdoc(swaggerOptions);
app.use("/api-docs", swaggerUi.serve, swaggerUi.setup(swaggerDocs));

app.use("/api/v1/create-subscription", createSubscription); 
app.use("/api/v1/get-user-by-stripe-id", getUserByStripeCustomerId); 

const PORT = process.env.PORT || 5000;
app.listen(PORT, async () => {
    await initDbConnection();
    console.log(`🚀 Server running on port ${PORT}`);
    await poolPromise; 
});
